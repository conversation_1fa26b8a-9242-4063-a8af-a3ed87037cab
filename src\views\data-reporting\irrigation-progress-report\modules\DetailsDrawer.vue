<template>
  <!-- 详情查看 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1500"
    @cancel="cancel"
    @ok="cancel"
    modalHeight="800"
  >
  <div slot="content">
    <div class="irrigation-progress-report">
      <!-- 基本信息部分 -->
      <div class="basic-info-section">
        <h3 class="section-title">基本信息</h3>
        <div class="form-row">
          <div class="form-item">
            <label>灌溉时间：</label>
            <span class="detail-value">
              {{ formData.irrigationPeriod && formData.irrigationPeriod.length === 2 
                ? `${formData.irrigationPeriod[0]} ~ ${formData.irrigationPeriod[1]}` 
                : '-' 
              }}
            </span>
          </div>
        </div>
      </div>

      <!-- 灌溉情况部分 -->
      <div class="irrigation-table-section">
        <h3 class="section-title">灌溉情况</h3>
        <vxe-table
          ref="irrigationTableRef"
          :key="tableKey"
          :data="tableData"
          border
          stripe
          :row-config="{ keyField: 'id' }"
          :column-config="{ resizable: true }"
          height="500"
          class="irrigation-table"
          width="100%"
          :merge-cells="mergeCells"
        >
          <!-- 单位列 -->
          <vxe-column field="unit" title="单位" width="120" align="center">
            <template #default="{ row }">
              <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal', color: '#333' }">
                {{ row.unit }}
              </span>
            </template>
          </vxe-column>

          <!-- 直口实引水量列 -->
          <vxe-column field="waterVolume" title="直口实引水量（流量日）" width="260" align="center">
            <template #default="{ row }">
              <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal', color: '#333' }">
                {{ row.unit === '备注' ? (formData.remark || '--') : (row.waterVolume || '-') }}
              </span>
            </template>
          </vxe-column>

          <!-- 浇地面积列组 -->
          <vxe-colgroup title="浇地面积（万亩）">
            <!-- 干地 -->
            <vxe-column field="dryLand" title="干地" width="150" align="center">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal', color: '#333' }">
                  {{ row.dryLand || '-' }}
                </span>
              </template>
            </vxe-column>

            <!-- 热水地 -->
            <vxe-column field="hotWaterLand" title="热水地" width="150" align="center">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal', color: '#333' }">
                  {{ row.hotWaterLand || '-' }}
                </span>
              </template>
            </vxe-column>

            <!-- 动态生成农作物列 -->
            <vxe-column v-for="crop in cropColumns" :key="crop.code" :field="crop.code" :title="crop.name" width="150" align="center">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal', color: '#333' }">
                  {{ row[crop.code] || '-' }}
                </span>
              </template>
            </vxe-column>
          </vxe-colgroup>
        </vxe-table>
      </div>


    </div>
  </div>
  </ant-modal>
</template>

<script lang="jsx">
import AntModal from '@/components/pt/dialog/AntModal'
import { getIrrigationSettingById, getDeptTree, getIrrigationRound } from '../service'
import moment from 'moment'

export default {
  name: 'DetailsDrawer',
  props: [],
  components: { AntModal },
  data() {
    return {
      open: false,
      modalLoading: false,
      formTitle: '',
      formData: {
        irrigationPeriod: null,
        remark: ''
      },
      tableData: [],
      cropColumns: [], // 农作物列配置
      deptList: [], // 部门列表
      tableKey: 0, // 用于强制重新渲染表格
      mergeCells: [] // 合并单元格配置
    }
  },
  methods: {
    cancel() {
      this.open = false
      this.resetData()
    },
    
    // 显示详情
    async showDetails(record) {
      this.open = true
      this.formTitle = '详情'
      await this.loadDetailData(record)
    },

    // 重置数据
    resetData() {
      this.formData = {
        irrigationPeriod: null,
        remark: ''
      }
      this.tableData = []
      this.cropColumns = []
      this.tableKey = 0
      this.mergeCells = []
    },

    // 加载详情数据
    async loadDetailData(record) {
      try {
        this.modalLoading = true
        
        // 获取详情数据
        const response = await getIrrigationSettingById(record.id)
        if (response.success) {
          const data = response.data
          
          // 设置基本信息
          this.formData.irrigationPeriod = [
            moment(data.startTime).format('YYYY-MM-DD'),
            moment(data.endTime).format('YYYY-MM-DD')
          ]
          
          // 加载农作物列配置
          await this.loadCropColumns(data.startTime, data.endTime)
          
          // 加载部门数据
          await this.loadDeptData()
          
          // 初始化表格数据
          this.initTableData()
          
          // 填充详情数据
          this.fillDetailData(data)
          
          // 强制刷新表格
          this.tableKey += 1
        } else {
          this.$message.error(response.message || '获取数据失败')
        }
      } catch (error) {
        console.error('加载详情数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.modalLoading = false
      }
    },

    // 加载部门数据
    async loadDeptData() {
      try {
        const response = await getDeptTree()
        if (response.success && response.data && response.data.length > 0) {
          // 获取第一个children内的数组（各供水所）
          const firstLevel = response.data[0]
          if (firstLevel && firstLevel.children) {
            this.deptList = firstLevel.children
          }
        }
      } catch (error) {
        console.error('加载部门数据失败:', error)
      }
    },

    // 加载农作物列配置
    async loadCropColumns(startDate, endDate) {
      try {
        // 修正接口调用方式，与新增页面保持一致
        const response = await getIrrigationRound(endDate, startDate)
        console.log('详情页获取灌溉轮次响应:', response)
        if (response.success) {
          this.cropColumns = []
          
          // 直接使用接口返回的cropCodes和cropCodeNames
          if (response.data.cropCodes && response.data.cropCodeNames && 
              response.data.cropCodes.length === response.data.cropCodeNames.length) {
            const newCropColumns = []
            for (let i = 0; i < response.data.cropCodes.length; i++) {
              newCropColumns.push({
                code: response.data.cropCodes[i],
                name: response.data.cropCodeNames[i]
              })
            }
            // 使用Vue.set确保响应式更新
            this.$set(this, 'cropColumns', newCropColumns)
          }
          console.log('详情页生成的农作物列:', this.cropColumns)
        }
      } catch (error) {
        console.error('加载农作物数据失败:', error)
      }
    },

    // 初始化表格数据
    initTableData() {
      this.tableData = []
      
      // 添加部门数据
      this.deptList.forEach((dept, index) => {
        const row = {
          id: index + 1,
          unit: dept.deptName,
          deptId: dept.deptId,
          waterVolume: null,
          dryLand: null,
          hotWaterLand: null,
          isTotal: false
        }
        
        // 为每个农作物字段初始化数据
        this.cropColumns.forEach(crop => {
          row[crop.code] = null
        })
        
        this.tableData.push(row)
      })
      
      // 添加固定行
      const fixedRows = [
        { name: '全灌域', isTotal: true },
        { name: '报总局', isTotal: false },
        { name: '备注', isTotal: false }
      ]
      
      fixedRows.forEach((fixedRow, index) => {
        const row = {
          id: this.deptList.length + index + 1,
          unit: fixedRow.name,
          deptId: null,
          waterVolume: null,
          dryLand: null,
          hotWaterLand: null,
          isTotal: fixedRow.isTotal
        }
        
        // 为每个农作物字段初始化数据
        this.cropColumns.forEach(crop => {
          row[crop.code] = null
        })
        
        this.tableData.push(row)
      })
      
      // 计算合并单元格配置
      this.calculateMergeCells()
    },

    // 填充详情数据
    fillDetailData(data) {
      if (data.cstIrrigationProgressDetailDtoList) {
        data.cstIrrigationProgressDetailDtoList.forEach(detail => {
          let row
          
          // 特殊处理：depId为1的数据渲染到报总局行
          if (detail.depId === 0) {
            row = this.tableData.find(r => r.unit === '报总局')
          } else {
            row = this.tableData.find(r => r.deptId === detail.depId)
          }
          
          if (row) {
            row.waterVolume = detail.waterAmount
            row.dryLand = detail.dryLandPourAmount
            row.hotWaterLand = detail.hotWaterLandPourAmount
            
            // 填充农作物数据
            if (detail.cropPourAreaDetail) {
              detail.cropPourAreaDetail.forEach(crop => {
                row[crop.code] = crop.pourAmount
              })
            }
          }
        })
      }
      
      // 设置备注信息
      this.formData.remark = data.remark || ''
      
      // 计算全灌域数据
      this.calculateTotalData()
    },

    // 计算全灌域合计数据
    calculateTotalData() {
      // 找到全灌域行
      const totalRow = this.tableData.find(row => row.unit === '全灌域')
      if (!totalRow) return

      // 找到需要计算合计的行（只计算部门数据）
      const dataRows = this.tableData.filter(row => 
        row.deptId && !row.isTotal
      )

      // 计算各字段的合计
      const fields = ['waterVolume', 'dryLand', 'hotWaterLand', ...this.cropColumns.map(crop => crop.code)]

      fields.forEach(field => {
        let total = 0
        dataRows.forEach(row => {
          const value = parseFloat(row[field]) || 0
          total += value
        })
        totalRow[field] = total > 0 ? total.toFixed(2) : null
      })
    },

    // 计算合并单元格配置
    calculateMergeCells() {
      this.mergeCells = []
      
      // 找到备注行的索引
      const remarkRowIndex = this.tableData.findIndex(row => row.unit === '备注')
      if (remarkRowIndex !== -1) {
        // 计算需要合并的列数：直口实引水量 + 干地 + 热水地 + 农作物列数
        const mergeColspan = 1 + 1 + 1 + this.cropColumns.length // 1(直口实引水量) + 1(干地) + 1(热水地) + 农作物列数
        
        // 合并备注行从第二列开始的所有列
        this.mergeCells.push({
          row: remarkRowIndex,
          col: 1, // 从第二列开始（0是单位列，1是直口实引水量列）
          rowspan: 1,
          colspan: mergeColspan
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.irrigation-progress-report {
  background: #fff;

  .basic-info-section {
    margin-bottom: 30px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    
    .form-row {
      display: flex;
      gap: 30px;
      
      .form-item {
        display: flex;
        align-items: center;
        
        label {
          margin-right: 8px;
          font-weight: 500;
          color: #333;
        }

        .detail-value {
          color: #666;
          font-size: 14px;
          padding: 4px 8px;
          // background-color: #f5f5f5;
          border-radius: 4px;
          min-width: 200px;
        }
      }
    }
  }

  .irrigation-table-section {
    margin-bottom: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    
    .irrigation-table {
      width: 100%;
      
      /deep/ .vxe-table--border-line {
        border-color: #e8e8e8;
      }
      
      /deep/ .vxe-header--column {
        background-color: #fafafa;
        font-weight: 600;
      }
      
      /deep/ .vxe-body--row {
        &:nth-child(even) {
          background-color: #fafafa;
        }
      }
      
      /deep/ .vxe-table {
        width: 100% !important;
      }
    }
  }


}
</style>