<template>
  <div class="common-table-page">
    <!-- 筛选栏 -->
    <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
      <a-form-item label="年份">
        <a-date-picker
          mode="year"
          format="YYYY"
          v-model="queryParam.year"
          placeholder="请选择"
          allow-clear
          :open="yearShowOne"
          style="width: 100%"
          @keyup.enter.native="handleQuery"
          @openChange="openChangeOne"
          @panelChange="panelChangeOne"
        ></a-date-picker>
      </a-form-item>

      <a-form-item label="填报单位">
        <a-select v-model="queryParam.reportUnit" placeholder="请选择" allow-clear @keyup.enter.native="handleQuery">
          <a-select-option
            v-for="option in reportUnitOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <template #table>
        <VxeTable
          ref="vxeTableRef"
          tableTitle="公管渠水量水费填报"
          :columns="columns"
          :tableData="list"
          :loading="loading"
          :isAdaptPageSize="true"
          @adaptPageSizeChange="adaptPageSizeChange"
          @refresh="getList"
          @selectChange="selectChange"
          @sortChange="sortChange"
          :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
          @handlePageChange="handlePageChange"
        >
          <div class="table-operations" slot="button">
            <a-button type="primary" @click="handleAdd()">
              <a-icon type="plus" />
              新增
            </a-button>
            <a-button type="primary" @click="handleImport()">
              <a-icon type="import" />
              导入
            </a-button>
            <a-button type="primary" @click="handleExport()">
              <a-icon type="export" />
              导出
            </a-button>
          </div>
        </VxeTable>

        <FormDrawer v-if="showFormDrawer" ref="formDrawerRef" @ok="getList" @close="showFormDrawer = false" />
        <DetailDrawer v-if="showDetails" ref="detailsRef" @ok="getList" @close="showDetails = false" />
      </template>
    </VxeTableForm>
  </div>
</template>

<script lang="jsx">
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import FormDrawer from './modules/FormDrawer.vue'
  import DetailDrawer from './modules/DetailDrawer.vue'
  import moment from 'moment'
  import { getPublicWaterVolumeBillList, deletePublicWaterVolumeBill } from './service'
  import { getOrgTree } from '@/api/user'

  export default {
    name: 'PublicWaterVolumeBillReport',
    components: {
      VxeTableForm,
      VxeTable,
      FormDrawer,
      DetailDrawer,
    },
    data() {
      return {
        // 查询参数
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          year: null,
          reportUnit: null,
          sort: []
        },
        // 年份选择器状态
        yearShowOne: false,
        // 表格数据
        list: [],
        total: 0,
        loading: false,
        // 选中的数据
        ids: [],
        names: [],
        isChecked: false,
        // 弹窗状态
        showFormDrawer: false,
        showDetails: false,
        // 填报单位选项
        reportUnitOptions: [],
        orgTree: [],
        // 表格列配置
        columns: [
          {
            type: 'seq',
            title: '序号',
            minWidth: 80,
            align: 'center'
          },
          {
            field: 'fillYear',
            title: '年份',
            minWidth: 100,
            align: 'center'
          },
          {
            field: 'depName',
            title: '填报单位',
            minWidth: 200,
            align: 'center'
          },
          {
            field: 'fillTime',
            title: '提交日期',
            minWidth: 150,
            align: 'center'
          },
          {
            field: 'fillUserName',
            title: '提交人',
            minWidth: 120,
            align: 'center'
          },
          {
            field: 'action',
            title: '操作',
            minWidth: 200,
            slots: {
              default: ({ row }) => {
                return (
                  <span>
                    <a onClick={() => this.handleDetails(row)}>查看</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleUpdate(row)}>编辑</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              }
            },
          }
        ]
      }
    },
    created() {
      this.getOrgTreeData()
      this.getList()
    },
    methods: {
      // 查询
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      // 重置查询
      resetQuery() {
        this.queryParam = {
          pageNum: 1,
          pageSize: 10,
          year: null,
          reportUnit: null,
          sort: []
        }
        this.getList()
      },
      // 年份选择器打开状态变化
      openChangeOne(status) {
        this.yearShowOne = status
      },
      // 年份选择器面板变化
      panelChangeOne(value) {
        this.queryParam.year = moment(value).format('YYYY')
        this.yearShowOne = false
        // this.handleQuery()
      },
      // 获取组织机构树数据
      async getOrgTreeData() {
        try {
          const response = await getOrgTree()
          if (response.success && response.data) {
            this.orgTree = response.data
            this.buildReportUnitOptions()
          }
        } catch (error) {
          console.error('获取组织机构树失败:', error)
        }
      },
      // 构建填报单位选项
      buildReportUnitOptions() {
        const options = []
        // 优先使用loginOrgId，其次使用deptId，最后使用默认值
        const currentUserDepId = this.$store.state.user.loginOrgId || this.$store.state.user.deptId || 10020

        console.log('当前用户部门ID:', currentUserDepId)
        console.log('用户store信息:', this.$store.state.user)

        // 递归查找匹配的部门
        const findMatchingDept = (nodes, targetDepId) => {
          for (const node of nodes) {
            // 确保ID比较时类型一致
            if (Number(node.deptId) === Number(targetDepId)) {
              return node
            }
            if (node.children && node.children.length > 0) {
              const found = findMatchingDept(node.children, targetDepId)
              if (found) return found
            }
          }
          return null
        }

        const matchedDept = findMatchingDept(this.orgTree, currentUserDepId)

        console.log('匹配到的部门:', matchedDept)

        if (matchedDept) {
          // 添加当前部门
          options.push({
            value: matchedDept.deptName,
            label: matchedDept.deptName,
            deptId: matchedDept.deptId
          })

          // 如果有子部门，也添加子部门
          if (matchedDept.children && matchedDept.children.length > 0) {
            matchedDept.children.forEach(child => {
              options.push({
                value: child.deptName,
                label: child.deptName,
                deptId: child.deptId
              })
            })
          }
        }

        this.reportUnitOptions = options
        console.log('构建的填报单位选项:', this.reportUnitOptions)
      },
      // 获取列表数据
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })

        // 构建请求参数
        const params = {
          pageNum: this.queryParam.pageNum,
          pageSize: this.queryParam.pageSize,
          fillYear: this.queryParam.year ? Number(this.queryParam.year) : null,
        }

        // 如果选择了填报单位，则添加对应的depId
        if (this.queryParam.reportUnit) {
          const selectedUnit = this.reportUnitOptions.find(option => option.value === this.queryParam.reportUnit)
          if (selectedUnit && selectedUnit.deptId) {
            params.depId = selectedUnit.deptId
          }
        }

        // 暂时使用测试数据，不对接接口
        // setTimeout(() => {
        //   this.list = this.getTestData()
        //   this.total = this.list.length
        //   this.loading = false
        // }, 500)

        // 真实接口调用（暂时注释）
        getPublicWaterVolumeBillList(params).then(response => {
          if (response.success) {
            this.list = response.data.data || []
            this.total = response.data.total || 0
          }
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      },
      // 获取测试数据
      getTestData() {
        return [
          {
            id: 1,
            year: '2024',
            reportUnitName: '某某灌区管理处',
            submitDate: '2024-03-15',
            submitUserName: '张三',
            depId: 10021
          },
          {
            id: 2,
            year: '2023',
            reportUnitName: '某某水利工程管理处',
            submitDate: '2023-12-20',
            submitUserName: '李四',
            depId: 10022
          }
        ]
      },
      // 自适应页面大小变化
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.id)
        this.names = valObj.records.map(item => item.reportUnitName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },
      // 新增
      handleAdd() {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleAdd())
      },
      // 修改
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },
      // 查看详情
      handleDetails(record) {
        this.showDetails = true
        this.$nextTick(() => this.$refs.detailsRef.handleView(record))
      },
      // 删除
      async handleDelete(record) {
        try {
          // 暂时使用模拟删除，不对接接口   
          // this.$message.success('删除成功')
          // this.getList()
          var that = this
          const id = record.id
          const name = record.depName
          // 真实接口调用（暂时注释）
          this.$confirm({
            title: '确认删除所选中数据?',
            content: '当前选中填报单位为"' + name + '"的数据',
            onOk() {
              that.deleteData(id)
            },
            onCancel() {},
          })
          // const response = await deletePublicWaterVolumeBill(record.id)
          // if (response.success) {
          //   this.$message.success('删除成功')
          //   this.getList()
          // } else {
          //   this.$message.error(response.message || '删除失败')
          // }
        } catch (error) {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        }
      },
      /** 执行删除操作 */
      async deleteData(id) {
        try {
          this.loading = true
          const response = await deletePublicWaterVolumeBill(id)
          
          if (response.code === 200) {
            this.$message.success('删除成功')
            this.selectChange({ records: [] })
            this.getList()
          } else {
            this.$message.error(response.message || '删除失败')
          }
        } catch (error) {
          console.error('删除数据失败:', error)
          this.$message.error('删除失败: ' + (error.message || '网络错误'))
        } finally {
          this.loading = false
        }
      },
      // 导入
      handleImport() {
        this.$message.info('导入功能待开发')
      },
      // 导出
      handleExport() {
        this.$message.info('导出功能待开发')
      }
    }
  }
</script>