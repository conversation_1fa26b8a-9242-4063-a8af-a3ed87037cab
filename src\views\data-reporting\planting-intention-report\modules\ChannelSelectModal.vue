<template>
  <a-modal
    title="选择渠道"
    :visible="visible"
    :width="1500"
    :destroyOnClose="true"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <div class="channel-select-modal tree-table-page">
      <!-- 左侧基础类别 -->
      <div class="tree-table-tree-panel">
        <div class="tree-panel">
          <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
            <a-tab-pane key="1" tab="按行政区划" class="tab-pane">
              <TreeGeneral
                v-if="treeTabKey === '1'"
                :key="1"
                hasTab
                class="type-tree-panel"
                ref="treeGeneralRef"
                :treeOptions="districtTreeOptions"
                @onTreeMounted="onTreeMounted"
                @select="node => clickTreeNode(node)"
              />
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>

      <!-- 右侧穿梭框区域 -->
      <div class="tree-table-right-panel">
        <div class="table-panel" layout="vertical">
          <!-- 数据授权 渠道类 -->
          <div class="transfer-panel" v-if="isShowTransfer" :title="tableTitle">
            <!-- 左侧 选择项 -->
            <div class="left-table">
              <div class="tab-title">
                <div class="left-span">选择项</div>
                <div class="right-span">共{{ leftDataTotal }}项</div>
              </div>

              <div class="tab-item">
                <div class="item-row">
                  <div class="item-col">
                    渠道编码：
                    <a-input
                      class="tab-input"
                      v-model="leftParam.projectCode"
                      placeholder="请输入渠道编码"
                      allow-clear
                      @keyup.enter.native="handleQuery"
                    />
                  </div>
                  <div class="item-col">
                    渠道名称：
                    <a-input
                      class="tab-input"
                      v-model="leftParam.projectName"
                      placeholder="请输入渠道名称"
                      allow-clear
                      @keyup.enter.native="handleQuery"
                    />
                  </div>
                  <div class="item-col item-col-buttons">
                    <a-button type="primary" size="small" @click="handleQuery">查询</a-button>
                    <a-button size="small" @click="handleLeftReset">重置</a-button>
                  </div>
                </div>
              </div>

              <VxeTable
                ref="leftTableRef"
                class="vxetab-table"
                :otherHeight="40"
                :isShowTableHeader="true"
                :columns="leftColumns"
                :tableData="leftData"
                :loading="leftLoading"
                :isAdaptPageSize="true"
                @adaptPageSizeChange="adaptPageSizeChange"
                @selectChange="leftSelectChange"
                :tablePage="{ pageNum: leftParam.pageNum, pageSize: leftParam.pageSize, total: leftDataTotal }"
                @handlePageChange="handleLeftPageChange"
              />
            </div>

            <!-- 中间按钮 -->
            <div class="table-button">
              <div class="tab-group-div">
                <div class="tab-group">
                  <a-button size="small" class="tab-btn" type="primary" :disabled="!hasSelected" @click="addChannel">
                    <a-icon type="right" />
                  </a-button>
                  <a-button size="small" class="tab-btn" type="primary" :disabled="!hasRightSelected" @click="removeChannel">
                    <a-icon type="left" />
                  </a-button>
                </div>
              </div>
            </div>

            <!-- 右侧 已选项 -->
            <div class="right-table">
              <div class="tab-title">
                <div class="left-span">已选项</div>
                <div class="right-span">共{{ rightDataTotal }}项</div>
              </div>

              <div class="tab-item">
                <div class="item-row">
                  <div class="item-col">
                    渠道编码：
                    <a-input
                      class="tab-input"
                      v-model="rightParam.projectCode"
                      placeholder="请输入渠道编码"
                      allow-clear
                      @keyup.enter.native="handleRightQuery"
                    />
                  </div>
                  <div class="item-col">
                    渠道名称：
                    <a-input
                      class="tab-input"
                      v-model="rightParam.projectName"
                      placeholder="请输入渠道名称"
                      allow-clear
                      @keyup.enter.native="handleRightQuery"
                    />
                  </div>
                  <div class="item-col item-col-buttons">
                    <a-button type="primary" size="small" @click="handleRightQuery">查询</a-button>
                    <a-button size="small" @click="handleRightReset">重置</a-button>
                  </div>
                </div>
              </div>

              <VxeTable
                ref="rightTableRef"
                class="vxetab-table"
                :otherHeight="40"
                :isShowTableHeader="true"
                :columns="rightColumns"
                :tableData="rightData"
                :loading="rightLoading"
                :isAdaptPageSize="true"
                @adaptPageSizeChange="adaptPageSizeChange"
                @selectChange="rightSelectChange"
                :tablePage="{ pageNum: rightParam.pageNum, pageSize: rightParam.pageSize, total: rightDataTotal }"
                @handlePageChange="handleRightPageChange"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import VxeTable from '@/components/VxeTable'
import TreeGeneral from '@/components/TreeGeneral'
import { getDistrictTree, getProjectPage } from '@/api/common'

export default {
  name: 'ChannelSelectModal',
  components: {
    VxeTable,
    TreeGeneral
  },
  data() {
    return {
      visible: false,
      
      // 行政区划树形组件配置
      districtTreeOptions: {
        dataSource: [],
        replaceFields: {
          children: 'children',
          title: 'districtName',
          key: 'districtId'
        }
      },
      
      // 当前选中的区划和类别
      selectedDistrict: null,
      selectedCategory: null,
      tableTitle: '渠道选择',
      
      // Tabs相关
      treeTabKey: '1',
      isShowTransfer: false,
      
      // 当前选中的ID
      selectedDistrictId: null,
      
      // 左侧参数
      leftParam: {
        pageNum: 1,
        pageSize: 10,
        projectCode: '',
        projectName: ''
      },
      
      // 右侧参数
      rightParam: {
        pageNum: 1,
        pageSize: 10,
        projectCode: '',
        projectName: ''
      },
      
      // 左侧数据
      leftData: [],
      leftDataTotal: 0,
      leftLoading: false,
      leftSelectedIds: [],
      leftSelectedRows: [],
      hasSelected: false,
      
      // 右侧数据
      rightData: [],
      rightDataTotal: 0,
      rightLoading: false,
      rightSelectedIds: [],
      rightSelectedRows: [],
      hasRightSelected: false,
      
      // 已选择的项目ID
      selectedProjectIds: [],
      
      // 缓存所有项目数据，避免重复API调用
      allProjectsCache: [],
      
      // 左侧表格列
      leftColumns: [
        { type: 'checkbox', width: 50 },
        {
          title: '渠道编号',
          field: 'projectCode',
          minWidth: 120
        },
        {
          title: '渠道名称',
          field: 'projectName',
          minWidth: 150
        }
      ],
      
      // 右侧表格列
      rightColumns: [
        { type: 'checkbox', width: 50 },
        {
          title: '渠道编号',
          field: 'projectCode',
          minWidth: 120
        },
        {
          title: '渠道名称',
          field: 'projectName',
          minWidth: 150
        }
      ]
    }
  },
  mounted() {
    // 组件挂载时加载行政区划数据
    this.loadDistrictTree()
  },
  methods: {
    // 加载行政区划树数据
    async loadDistrictTree() {
      try {
        const response = await getDistrictTree()
        if (response.code === 200 && response.data) {
          this.districtTreeOptions.dataSource = response.data
          
          // 如果弹窗已经显示且没有选中区划，自动选中第一个
          if (this.visible && !this.selectedDistrict && response.data.length > 0) {
            await this.$nextTick()
            const firstNode = response.data[0]
            this.clickTreeNode(firstNode)
          }
        }
      } catch (error) {
        console.error('加载行政区划数据失败:', error)
        this.$message.error('加载行政区划数据失败')
      }
    },
    
    // 显示弹窗
    show(selectedProjects = []) {
      this.visible = true
      this.selectedProjectIds = selectedProjects.map(item => item.projectId || item.id)
      
      // 使用 nextTick 确保 DOM 更新完成后再执行自动选择
      this.$nextTick(() => {
        // 如果树数据已经加载，自动选中第一个节点
        if (this.districtTreeOptions.dataSource.length > 0) {
          const firstNode = this.districtTreeOptions.dataSource[0]
          this.clickTreeNode(firstNode)
        } else {
          // 如果树数据还没加载，只加载右侧已选数据
          this.loadRightData()
        }
      })
    },
    
    // 隐藏弹窗
    hide() {
      this.visible = false
      this.resetData()
    },
    
    // 重置数据
    resetData() {
      this.selectedDistrict = null
      this.selectedCategory = null
      this.selectedDistrictId = null
      this.treeTabKey = '1'
      this.isShowTransfer = false
      this.leftParam = { pageNum: 1, pageSize: 10, projectCode: '', projectName: '' }
      this.rightParam = { pageNum: 1, pageSize: 10, projectCode: '', projectName: '' }
      this.leftSelectedIds = []
      this.leftSelectedRows = []
      this.rightSelectedIds = []
      this.rightSelectedRows = []
      this.hasSelected = false
      this.hasRightSelected = false
      this.leftData = []
      this.rightData = []
      this.leftDataTotal = 0
      this.rightDataTotal = 0
      this.allProjectsCache = []
    },
    
    // 树组件挂载完成
    onTreeMounted(treeData) {
      
      // 只有在弹窗显示且没有选中任何区划时才自动选中第一个节点
      if (this.visible && !this.selectedDistrict && treeData && treeData.length > 0) {
        const firstNode = treeData[0]
        
        // 使用延迟确保树组件完全初始化
        setTimeout(() => {
          this.clickTreeNode(firstNode)
        }, 100)
      }
    },
    
    // 点击树节点
    clickTreeNode(node) {
      const nodeData = node.dataRef || node

      // 确保有districtCode才进行查询
      if (!nodeData.districtCode) {
        console.warn('节点没有districtCode，无法查询项目数据')
        this.isShowTransfer = false
        return
      }
      this.selectedDistrict = nodeData.districtCode
      this.selectedCategory = nodeData.districtName
      this.selectedDistrictId = nodeData.districtId
      this.isShowTransfer = true
      this.$nextTick(() => {
        this.loadLeftData()
      })
    },
    
    // 加载左侧数据
    async loadLeftData() {
      if (!this.selectedDistrict) {
        this.leftData = []
        this.leftDataTotal = 0
        return
      }
      
      this.leftLoading = true
      
      try {
        // 如果缓存中没有当前区划的数据，才调用API
        const cacheKey = this.selectedDistrict
        if (!this.allProjectsCache.find(cache => cache.districtCode === cacheKey)) {
          const params = {
            pageNum: 1,
            pageSize: 1000, // 获取所有数据
            districtCode: this.selectedDistrict
          }
          const response = await getProjectPage(params)
          
          if (response.code === 200 && response.data) {
            // 对项目数据进行过滤：过滤掉objectCategoryId !== 40 和 projectName中带有"闸"字的
            const filteredProjects = (response.data.data || []).filter(project => {
              // 过滤条件1：objectCategoryId 必须等于 40
              if (project.objectCategoryId !== 40) {
                return false
              }
              // 过滤条件2：projectName 不能包含"闸"字
              if (project.projectName && project.projectName.includes('闸')) {
                return false
              }
              return true
            })
            
            // 缓存当前区划的过滤后项目数据
            this.allProjectsCache.push({
              districtCode: cacheKey,
              projects: filteredProjects
            })
          }
        }
        
        // 从缓存中获取数据并进行过滤和分页
        this.filterAndPaginateLeftData()
        
      } catch (error) {
        console.error('加载项目数据失败:', error)
        this.$message.error('加载项目数据失败')
        this.leftData = []
        this.leftDataTotal = 0
      } finally {
        this.leftLoading = false
      }
    },
    
    // 过滤和分页左侧数据
    filterAndPaginateLeftData() {
      const cacheData = this.allProjectsCache.find(cache => cache.districtCode === this.selectedDistrict)
      if (!cacheData) {
        this.leftData = []
        this.leftDataTotal = 0
        return
      }
      
      let filteredData = cacheData.projects.filter(item => !this.selectedProjectIds.includes(item.projectId))
      
      // 按编码过滤
      if (this.leftParam.projectCode) {
        filteredData = filteredData.filter(item => 
          item.projectCode.toLowerCase().includes(this.leftParam.projectCode.toLowerCase())
        )
      }
      
      // 按名称过滤
      if (this.leftParam.projectName) {
        filteredData = filteredData.filter(item => 
          item.projectName.toLowerCase().includes(this.leftParam.projectName.toLowerCase())
        )
      }
      
      this.leftDataTotal = filteredData.length
      
      // 分页
      const start = (this.leftParam.pageNum - 1) * this.leftParam.pageSize
      const end = start + this.leftParam.pageSize
      this.leftData = filteredData.slice(start, end)
    },
    
    // 加载右侧数据
    loadRightData() {
      if (!this.selectedProjectIds.length) {
        this.rightData = []
        this.rightDataTotal = 0
        return
      }
      
      // 从缓存中获取已选择的项目数据
      this.filterAndPaginateRightData()
    },
    
    // 过滤和分页右侧数据
    filterAndPaginateRightData() {
      // 从所有缓存中找到已选择的项目
      let selectedProjects = []
      const seenProjectIds = new Set() // 用于去重
      
      this.allProjectsCache.forEach(cache => {
        const projects = cache.projects.filter(item => {
          // 确保项目ID在selectedProjectIds中且未重复添加
          return this.selectedProjectIds.includes(item.projectId) && !seenProjectIds.has(item.projectId)
        })
        
        projects.forEach(project => {
          seenProjectIds.add(project.projectId)
          selectedProjects.push(project)
        })
      })
      
      // 按编码过滤
      if (this.rightParam.projectCode) {
        selectedProjects = selectedProjects.filter(item => 
          item.projectCode.toLowerCase().includes(this.rightParam.projectCode.toLowerCase())
        )
      }
      
      // 按名称过滤
      if (this.rightParam.projectName) {
        selectedProjects = selectedProjects.filter(item => 
          item.projectName.toLowerCase().includes(this.rightParam.projectName.toLowerCase())
        )
      }
      
      this.rightDataTotal = selectedProjects.length
      
      // 分页
      const start = (this.rightParam.pageNum - 1) * this.rightParam.pageSize
      const end = start + this.rightParam.pageSize
      this.rightData = selectedProjects.slice(start, end)
      
    },
    
    // 左侧查询
    handleQuery() {
      this.leftParam.pageNum = 1
      this.filterAndPaginateLeftData()
    },
    
    // 右侧查询
    handleRightQuery() {
      this.rightParam.pageNum = 1
      this.filterAndPaginateRightData()
    },
    
    // 左侧重置
    handleLeftReset() {
      this.leftParam.projectCode = ''
      this.leftParam.projectName = ''
      this.leftParam.pageNum = 1
      this.filterAndPaginateLeftData()
    },
    
    // 右侧重置
    handleRightReset() {
      this.rightParam.projectCode = ''
      this.rightParam.projectName = ''
      this.rightParam.pageNum = 1
      this.filterAndPaginateRightData()
    },
    
    // 左侧分页变化
    handleLeftPageChange({ currentPage, pageSize }) {
      this.leftParam.pageNum = currentPage
      this.leftParam.pageSize = pageSize
      this.filterAndPaginateLeftData()
    },
    
    // 右侧分页变化
    handleRightPageChange({ currentPage, pageSize }) {
      this.rightParam.pageNum = currentPage
      this.rightParam.pageSize = pageSize
      this.filterAndPaginateRightData()
    },
    
    // 页面大小变化
    adaptPageSizeChange(pageSize) {
      this.leftParam.pageSize = pageSize
      this.rightParam.pageSize = pageSize
      this.filterAndPaginateLeftData()
      this.filterAndPaginateRightData()
    },
    
    // 左侧选择变化
    leftSelectChange(valObj) {
      this.leftSelectedIds = valObj.records.map(item => item.projectId)
      this.leftSelectedRows = valObj.records
      this.hasSelected = !!valObj.records.length
    },
    
    // 右侧选择变化
    rightSelectChange(valObj) {
      this.rightSelectedIds = valObj.records.map(item => item.projectId)
      this.rightSelectedRows = valObj.records
      this.hasRightSelected = !!valObj.records.length
    },
    
    // 添加项目
    addChannel() {
      
      let addedCount = 0
      this.leftSelectedRows.forEach(item => {
        if (!this.selectedProjectIds.includes(item.projectId)) {
          this.selectedProjectIds.push(item.projectId)
          addedCount++
        } else {
          console.warn('⚠️ 项目已存在，跳过:', item.projectCode)
        }
      })
      
      // 直接更新数据显示，不重新调用API
      this.filterAndPaginateLeftData()
      this.filterAndPaginateRightData()
      
      // 清空选择状态
      this.clearLeftSelection()
    },
    
    // 移除项目
    removeChannel() {
      
      this.rightSelectedRows.forEach(item => {
        const index = this.selectedProjectIds.indexOf(item.projectId)
        if (index > -1) {
          this.selectedProjectIds.splice(index, 1)
        }
      })
      
      // 直接更新数据显示，不重新调用API
      this.filterAndPaginateLeftData()
      this.filterAndPaginateRightData()
      
      // 清空选择状态
      this.clearRightSelection()
    },
    
    // 确认
    handleOk() {
      
      // 从缓存中获取所有选中的项目详细信息
      let selectedProjects = []
      const seenProjectIds = new Set() // 用于去重
      
      if (this.selectedProjectIds.length > 0) {
        this.allProjectsCache.forEach((cache, cacheIndex) => {
          
          const projects = cache.projects.filter(item => {
            const isSelected = this.selectedProjectIds.includes(item.projectId)
            const notSeen = !seenProjectIds.has(item.projectId)
            // 检查项目是否在选中列表中且尚未添加过
            return isSelected && notSeen
          })
          
          
          // 记录已添加的项目ID并添加到结果中
          projects.forEach(project => {
            seenProjectIds.add(project.projectId)
            selectedProjects.push(project)
          })
        })
      } else {
        console.log('selectedProjectIds为空，没有选中任何项目')
      }
      
      // 格式化数据，确保包含渠道名、渠道编码和渠道id
      const formattedProjects = selectedProjects.map(project => ({
        id: project.projectId,
        channelCode: project.projectCode,
        channelName: project.projectName,
        projectId: project.projectId,
        projectCode: project.projectCode,
        projectName: project.projectName,
        objectCategoryName: project.objectCategoryName
      }))
      this.$emit('ok', formattedProjects)
      this.hide()
    },
    
    // 取消
    handleCancel() {
      this.hide()
    },
    
    // 清空左侧选择状态
    clearLeftSelection() {
      this.leftSelectedIds = []
      this.leftSelectedRows = []
      this.hasSelected = false
      
      // 使用nextTick确保DOM更新后清空表格选择
      this.$nextTick(() => {
        if (this.$refs.leftTableRef && this.$refs.leftTableRef.$refs.vxeTableRef) {
          // VxeGrid的清空选择方法
          this.$refs.leftTableRef.$refs.vxeTableRef.clearCheckboxRow()
        }
      })
    },
    
    // 清空右侧选择状态
    clearRightSelection() {
      this.rightSelectedIds = []
      this.rightSelectedRows = []
      this.hasRightSelected = false
      
      // 使用nextTick确保DOM更新后清空表格选择
      this.$nextTick(() => {
        if (this.$refs.rightTableRef && this.$refs.rightTableRef.$refs.vxeTableRef) {
          // VxeGrid的清空选择方法
          this.$refs.rightTableRef.$refs.vxeTableRef.clearCheckboxRow()
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.channel-select-modal {
    &.tree-table-page {
    display: flex;
    height: 600px;
    
    .tree-table-tree-panel {
      height: 100%;
      width: 280px;
      border-right: 1px solid #e8e8e8;
      background: #fafafa;
      
      .tree-panel {
        height: 100%;
        
        ::v-deep .ant-tabs {
          height: 100%;
          display: flex;
          flex-direction: column;
          
          .ant-tabs-bar {
            margin-bottom: 0;
            border-bottom: 1px solid #e8e8e8;
          }
          
          .ant-tabs-content {
            flex: 1;
            padding: 8px;
            overflow: hidden; // 改为hidden，不显示滚动条
          }
          
          .type-tree-panel {
            height: 100%;
            overflow: hidden; // 改为hidden，不显示滚动条
            
            .ant-tree {
              background: transparent;
              
              .ant-tree-node-content-wrapper {
                padding: 2px 4px;
                border-radius: 2px;
                
                &:hover {
                  background-color: #e6f7ff;
                }
                
                &.ant-tree-node-selected {
                  background-color: #bae7ff;
                }
              }
            }
          }
          
          .tab-tree-panel-box {
            overflow-y: auto; // 保持这个，这是TreeGeneral组件内部的滚动
            overflow-x: hidden;
            height: calc(100% - 50px); // 减去搜索框的高度
            
            .ant-tree {
              background: transparent;
              
              .ant-tree-node-content-wrapper {
                padding: 2px 4px;
                border-radius: 2px;
                
                &:hover {
                  background-color: #e6f7ff;
                }
                
                &.ant-tree-node-selected {
                  background-color: #bae7ff;
                }
              }
            }
          }
        }
      }
    }
    
    .tree-table-right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .table-panel {
        flex: 1;
        // padding: 16px;
        
        .transfer-panel {
          display: flex;
          align-items: stretch;
          height: 100%;
          
          .left-table, .right-table {
            flex: 1;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            
            .tab-title {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 16px;
              background: #fafafa;
              border-bottom: 1px solid #e8e8e8;
              
              .left-span {
                font-weight: 500;
                color: #333;
              }
              
              .right-span {
                color: #666;
                font-size: 12px;
              }
            }
            
            .tab-item {
              padding: 12px 16px;
              border-bottom: 1px solid #e8e8e8;
              
              .item-row {
                display: flex;
                gap: 16px;
                align-items: center;
                
                .item-col {
                  flex: 1;
                  display: flex;
                  align-items: center;
                  
                  .tab-input {
                    flex: 1;
                    margin-left: 8px;
                  }
                  
                  &.item-col-buttons {
                    flex: none;
                    gap: 8px;
                    
                    .ant-btn {
                      margin-left: 8px;
                    }
                  }
                }
              }
            }
            
            .vxetab-table {
              flex: 1;
              border: none;
              border-radius: 0;
            }
          }
          
          .table-button {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            margin: 0 16px;
            
            .tab-group-div {
              .tab-group {
                display: flex;
                flex-direction: column;
                gap: 8px;
                
                .tab-btn {
                  width: 32px;
                  height: 32px;
                  padding: 0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 深度选择器样式
::v-deep .vxetab-table {
  // 表头样式可以在这里自定义
}

// 全局树结构滚动样式
::v-deep .tab-tree-panel-box {
  overflow-y: auto;
  overflow-x: hidden;
  
  .ant-tree {
    background: transparent;
    
    .ant-tree-node-content-wrapper {
      padding: 2px 4px;
      border-radius: 2px;
      
      &:hover {
        background-color: #e6f7ff;
      }
      
      &.ant-tree-node-selected {
        background-color: #bae7ff;
      }
    }
  }
}
</style> 