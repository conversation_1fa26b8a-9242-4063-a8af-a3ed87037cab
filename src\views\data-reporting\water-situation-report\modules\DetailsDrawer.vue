<template>
    <!-- 详情查看 -->
    <ant-modal
      :visible="open"
      :modal-title="formTitle"
      :loading="modalLoading"
      modalWidth="1500"
      @cancel="cancel"
      @ok="cancel"
      modalHeight="800"
    >
    <div slot="content">
      <div class="irrigation-rounds-crop-reporting">
        <!-- 基本信息部分 -->
        <div class="basic-info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="form-row">
            <div class="form-item">
              <label>日期：</label>
              <span class="info-value">{{ formatDate(formData.date) }}</span>
            </div>
            <div class="form-item">
              <label>轮次：</label>
              <span class="info-value">{{ getRoundLabel(formData.round) }}</span>
            </div>
          </div>
        </div>

        <!-- 水情表部分 -->
        <div class="water-table-section">
          <h3 class="section-title">水情表</h3>
          <vxe-table
            ref="waterTableRef"
            :data="tableData"
            border
            stripe
            :row-config="{ keyField: 'id' }"
            :column-config="{ resizable: true }"
            :span-method="mergeRowMethod"
            height="400"
            class="water-table"
            width="100%"
            header-align="center"
            align="center"
          >
            <!-- 灌域列 -->
            <vxe-column field="irrigationArea" title="灌域" width="100">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.irrigationArea }}
                </span>
              </template>
            </vxe-column>

            <!-- 渠道列 -->
            <vxe-column field="channelGroup" title="渠道" width="110">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.channelGroup }}
                </span>
              </template>
            </vxe-column>
            <vxe-column field="channel" title="" width="110">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.channel }}
                </span>
              </template>
            </vxe-column>

            <!-- 流量列组 -->
            <vxe-colgroup title="流量">
              <vxe-column field="flow8" title="8时" width="100">
                <template #default="{ row }">
                  <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                    {{ row.flow8 || '-' }}
                  </span>
                </template>
              </vxe-column>
              <vxe-column field="flow18" title="18时" width="100">
                <template #default="{ row }">
                  <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                    {{ row.flow18 || '-' }}
                  </span>
                </template>
              </vxe-column>
            </vxe-colgroup>

            <!-- 日均流量列 -->
            <vxe-column field="dailyFlow" title="日均流量（m³/s）" width="140">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.dailyFlow != null ? row.dailyFlow : '-' }}
                </span>
              </template>
            </vxe-column>

            <!-- 累计流量日列 -->
            <vxe-column field="cumulativeFlow" title="累计流量日" width="120">
              <template #default="{ row }">
                <span :style="{ fontWeight: row.isTotal ? 'bold' : 'normal' }">
                  {{ row.cumulativeFlow != null ? row.cumulativeFlow : '-' }}
                </span>
              </template>
            </vxe-column>

            <!-- 备注列 -->
            <vxe-column field="remark" title="备注" width="120">
              <template #default="{ row }">
                {{ row.remark || '-' }}
              </template>
            </vxe-column>

            <!-- 放关口列组 -->
            <vxe-colgroup title="放关口">
              <vxe-column field="openTime" title="放口" width="180">
                <template #default="{ row }">
                  <span>{{ formatTime(row.openTime) }}</span>
                </template>
              </vxe-column>
              <vxe-column field="closeTime" title="关口" width="180">
                <template #default="{ row }">
                  <span>{{ formatTime(row.closeTime) }}</span>
                </template>
              </vxe-column>
            </vxe-colgroup>
          </vxe-table>
        </div>

        <!-- 永济干渠水位流量表部分 -->
        <div class="canal-table-section" v-if="canalTableData.length > 0">
          <!-- <h3 class="section-title">永济干渠水位流量表</h3> -->
          <vxe-table
            ref="canalTableRef"
            :data="canalTableData"
            border
            stripe
            :row-config="{ keyField: 'id' }"
            :column-config="{ resizable: true }"
            :span-method="mergeCanalRowMethod"
            height="300"
            class="canal-table"
            width="100%"
            header-align="center"
            align="center"
          >
            <!-- 永济干渠列 -->
            <vxe-column field="canalName" title="永济干渠" width="150">
              <template #default="{ row }">
                {{ row.canalName }}
              </template>
            </vxe-column>

            <!-- 永济节制闸列 -->
            <vxe-column field="gateName" title="永济节制闸" width="120">
              <template #default="{ row }">
                {{ row.gateName }}
              </template>
            </vxe-column>

            <!-- 闸位列 -->
            <vxe-column field="gatePosition" title="" width="100">
              <template #default="{ row }">
                {{ row.gatePosition }}
              </template>
            </vxe-column>

            <!-- 水位列组 -->
            <vxe-colgroup title="水位（m）">
              <vxe-column field="waterLevel8" title="8时" width="120">
                <template #default="{ row }">
                  {{ row.waterLevel8 || '-' }}
                </template>
              </vxe-column>
              <vxe-column field="waterLevel18" title="18时" width="120">
                <template #default="{ row }">
                  {{ row.waterLevel18 || '-' }}
                </template>
              </vxe-column>
            </vxe-colgroup>

            <!-- 流量列组 -->
            <vxe-colgroup title="流量（m³/s）">
              <vxe-column field="canalFlow8" title="8时" width="120">
                <template #default="{ row }">
                  {{ row.canalFlow8 || '-' }}
                </template>
              </vxe-column>
              <vxe-column field="canalFlow18" title="18时" width="120">
                <template #default="{ row }">
                  {{ row.canalFlow18 || '-' }}
                </template>
              </vxe-column>
            </vxe-colgroup>
          </vxe-table>
        </div>
      </div>
    </div>
    
    </ant-modal>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import moment from 'moment'
  import { getIrrigationWaterSituationById, getProjectChildren } from '../service'

  export default {
    name: 'DetailsDrawer',
    props: [],
    components: { AntModal },
    data() {
      return {
        open: false,
        modalLoading: false,
        formTitle: '',
        formData: {
          date: null,
          round: null
        },
        roundOptions: [
          { value: 1, label: '春夏灌' },
          { value: 2, label: '秋灌' },
          { value: 3, label: '秋浇' }
        ],
        tableData: [],
        canalTableData: [], // 永济干渠水位流量表数据
        detailData: null // 详情数据
      }
    },
    methods: {
      cancel() {
        this.open = false
        this.resetData()
      },
      
      // 显示详情
      async showDetails(record) {
        this.open = true
        this.formTitle = '详情查看'
        this.modalLoading = true
        
        try {
          await this.loadData(record)
        } catch (error) {
          console.error('加载详情数据失败:', error)
          this.$message.error('加载详情数据失败')
        } finally {
          this.modalLoading = false
        }
      },
      
      // 重置数据
      resetData() {
        this.formData = {
          date: null,
          round: null
        }
        this.tableData = []
        this.canalTableData = []
        this.detailData = null
      },
      
      // 加载数据
      async loadData(record) {
        // 调用详情接口获取数据
        const response = await getIrrigationWaterSituationById(record.id)
        
        if (response.success) {
          this.detailData = response.data
          
          // 设置基本信息
          this.formData = {
            date: this.detailData.fillDate ? moment(this.detailData.fillDate) : null,
            round: this.detailData.roundType
          }
          
          // 动态构建表格数据
          await this.buildTableData()
          // 重新计算所有合计行
          this.recalculateAllTotals()
          await this.buildCanalTableData()
        } else {
          this.$message.error(response.message || '获取详情失败')
        }
      },
      
      // 构建水情表数据
      async buildTableData() {
        try {
          this.tableData = []
          let currentId = 1
          
          // 获取顶级节点（总干渠和永济灌区）
          const topLevelResponse = await getProjectChildren({ parentId: 0 })
          if (!topLevelResponse.success) {
            throw new Error('获取顶级节点失败')
          }
          
          const topLevelData = topLevelResponse.data || []
          
          // 查找总干渠和永济灌区
          const totalCanal = topLevelData.find(item => item.projectCode === 'GQ001')
          const yongjiIrrigation = topLevelData.find(item => item.projectCode === 'P001')
          
          // 添加总干渠首
          if (totalCanal) {
            const totalCanalData = this.getChannelData(totalCanal.projectCode || totalCanal.projectId)
            this.tableData.push({
              id: currentId++,
              projectId: totalCanal.projectId,
              projectCode: totalCanal.projectCode,
              irrigationArea: '总干渠首',
              channelGroup: '总干渠首',
              channel: '',
              isTotal: false,
              level: 0,
              hasSubChannels: false,
              ...totalCanalData
            })
          }
          
          // 处理永济灌区
          if (yongjiIrrigation) {
            // 获取永济灌区的子节点
            const yongjiChildrenResponse = await getProjectChildren({ parentId: yongjiIrrigation.projectId })
            if (yongjiChildrenResponse.success) {
              const yongjiChildren = yongjiChildrenResponse.data || []
              
              // 按sort排序
              yongjiChildren.sort((a, b) => a.sort - b.sort)
              
              // 查找永济干渠
              // const yongjiCanal = yongjiChildren.find(item => item.projectName === '永济渠')
              const yongjiCanal = yongjiChildren.find(item => item.projectCode === 'GQ002')
              
              // 添加永济灌域行
              const yongjiIrrigationData = this.getChannelData('GQ002')
              this.tableData.push({
                id: currentId++,
                projectId: yongjiIrrigation.projectId,
                projectCode: 'GQ002',
                irrigationArea: '永济灌域',
                channelGroup: '永济渠',
                channel: '',
                isTotal: false,
                level: 0,
                hasSubChannels: false,
                ...yongjiIrrigationData
              })
              
              // 添加其他分干渠（排除永济干渠）
              yongjiChildren.forEach(child => {
                if (child.projectCode !== 'GQ002') {
                  const displayName = child.projectNameAbbr || child.projectName
                  const childData = this.getChannelData(child.projectCode || child.projectId)
                  this.tableData.push({
                    id: currentId++,
                    projectId: child.projectId,
                    projectCode: child.projectCode,
                    irrigationArea: '',
                    channelGroup: displayName.replace('分干渠', '渠'),
                    channel: '',
                    isTotal: false,
                    level: 1,
                    hasSubChannels: false,
                    ...childData
                  })
                }
              })
              
              // 添加永济灌域合计（创建时先用空值，后面重新计算）
              this.tableData.push({
                id: currentId++,
                projectId: null,
                irrigationArea: '',
                channelGroup: '灌域合计',
                channel: '',
                isTotal: true,
                level: 1,
                hasSubChannels: false,
                flow8: '0.00',
                flow18: '0.00',
                dailyFlow: '0.00',
                cumulativeFlow: '0.00',
                remark: '',
                openTime: null,
                closeTime: null
              })
              
              // 处理永济干渠的子节点
              if (yongjiCanal) {
                await this.buildYongjiCanalData(yongjiCanal, currentId)
              }
            }
          }
          
        } catch (error) {
          console.error('构建表格数据失败:', error)
          this.$message.error('构建表格数据失败')
        }
      },
      
      // 构建永济干渠数据
      async buildYongjiCanalData(yongjiCanal, startId) {
        try {
          let currentId = startId
          
          // 获取永济干渠的子节点
          const canalChildrenResponse = await getProjectChildren({ parentId: yongjiCanal.projectId })
          if (canalChildrenResponse.success) {
            const canalChildren = canalChildrenResponse.data || []
            
            // 按sort排序
            canalChildren.sort((a, b) => a.sort - b.sort)
            
            // 过滤掉包含"闸"的项目
            const filteredChildren = canalChildren.filter(child => !child.projectName.includes('闸'))
            
            // 查找正稍分干渠和干渠直口
            const zhengshaoCanal = filteredChildren.find(item => item.projectName === '正稍分干渠')
            const directCanal = filteredChildren.find(item => item.projectName === '干渠直口')
            
            // 获取普通分干渠（排除正稍分干渠和干渠直口）
            const normalCanals = filteredChildren.filter(child => 
              child.projectName !== '正稍分干渠' && child.projectName !== '干渠直口'
            )
            
            // 添加永济渠灌域标题行，使用永济干渠的projectCode
            if (normalCanals.length > 0) {
              const yongjiCanalData = this.getChannelData(normalCanals[0].projectCode || normalCanals[0].projectId)
              this.tableData.push({
                id: currentId++,
                projectId: normalCanals[0].projectId,
                projectCode: normalCanals[0].projectCode,
                irrigationArea: '永济渠灌域',
                channelGroup: normalCanals[0].projectName.replace('分干渠', '渠'),
                channel: '',
                isTotal: false,
                level: 0,
                hasSubChannels: false,
                ...yongjiCanalData
              })
            }
            
            // 添加其他分干渠（从第二个开始）
            normalCanals.slice(1).forEach(child => {
              const childData = this.getChannelData(child.projectCode || child.projectId)
              this.tableData.push({
                id: currentId++,
                projectId: child.projectId,
                projectCode: child.projectCode,
                irrigationArea: '',
                channelGroup: child.projectName.replace('分干渠', '渠'),
                channel: '',
                isTotal: false,
                level: 1,
                hasSubChannels: false,
                ...childData
              })
            })
            
            // 处理正稍分干渠
            if (zhengshaoCanal) {
              await this.buildZhengshaoData(zhengshaoCanal, currentId)
              currentId += 10 // 预留ID空间
            }
            
            // 处理干渠直口
            if (directCanal) {
              await this.buildDirectCanalData(directCanal, currentId)
              currentId += 10 // 预留ID空间
            }
            
            // 添加永济渠合计（创建时先用空值，后面重新计算）
            this.tableData.push({
              id: currentId++,
              projectId: null,
              irrigationArea: '',
              channelGroup: '永济渠合计',
              channel: '',
              isTotal: true,
              level: 1,
              hasSubChannels: false,
              flow8: '0.00',
              flow18: '0.00',
              dailyFlow: '0.00',
              cumulativeFlow: '0.00',
              remark: '',
              openTime: null,
              closeTime: null
            })
            
          }
        } catch (error) {
          console.error('构建永济干渠数据失败:', error)
        }
      },
      
      // 构建正稍分干渠数据
      async buildZhengshaoData(zhengshaoCanal, startId) {
        try {
          let currentId = startId
          
          // 获取正稍分干渠的子节点
          const zhengshaoChildrenResponse = await getProjectChildren({ parentId: zhengshaoCanal.projectId })
          if (zhengshaoChildrenResponse.success) {
            const zhengshaoChildren = zhengshaoChildrenResponse.data || []
            
            // 按sort排序
            zhengshaoChildren.sort((a, b) => a.sort - b.sort)
            
            // 添加正稍渠主行，使用第一个子项的名称
            if (zhengshaoChildren.length > 0) {
              const firstChildData = this.getChannelData(zhengshaoChildren[0].projectCode || zhengshaoChildren[0].projectId)
              this.tableData.push({
                id: currentId++,
                projectId: zhengshaoChildren[0].projectId,
                projectCode: zhengshaoChildren[0].projectCode,
                irrigationArea: '',
                channelGroup: '正稍渠',
                channel: zhengshaoChildren[0].projectName,
                isTotal: false,
                level: 2,
                hasSubChannels: true,
                ...firstChildData
              })
            }
            
            // 添加其他子渠道（从第二个开始）
            zhengshaoChildren.slice(1).forEach(child => {
              const childData = this.getChannelData(child.projectCode || child.projectId)
              this.tableData.push({
                id: currentId++,
                projectId: child.projectId,
                projectCode: child.projectCode,
                irrigationArea: '',
                channelGroup: '',
                channel: child.projectName,
                isTotal: false,
                level: 2,
                hasSubChannels: true,
                ...childData
              })
            })
            
            // 添加正稍渠合计（创建时先用空值，后面重新计算）
            this.tableData.push({
              id: currentId++,
              projectId: null,
              irrigationArea: '',
              channelGroup: '',
              channel: '正稍渠合计',
              isTotal: true,
              level: 2,
              hasSubChannels: false,
              flow8: '0.00',
              flow18: '0.00',
              dailyFlow: '0.00',
              cumulativeFlow: '0.00',
              remark: '',
              openTime: null,
              closeTime: null
            })
          }
        } catch (error) {
          console.error('构建正稍分干渠数据失败:', error)
        }
      },
      
      // 构建干渠直口数据
      async buildDirectCanalData(directCanal, startId) {
        try {
          let currentId = startId
          
          // 获取干渠直口的子节点
          const directChildrenResponse = await getProjectChildren({ parentId: directCanal.projectId })
          if (directChildrenResponse.success) {
            const directChildren = directChildrenResponse.data || []
            
            // 按sort排序
            directChildren.sort((a, b) => a.sort - b.sort)
            
            // 添加干渠直口主行，使用第一个子项的名称
            if (directChildren.length > 0) {
              const firstChildData = this.getChannelData(directChildren[0].projectCode || directChildren[0].projectId)
              this.tableData.push({
                id: currentId++,
                projectId: directChildren[0].projectId,
                projectCode: directChildren[0].projectCode,
                irrigationArea: '',
                channelGroup: '干渠直口',
                channel: directChildren[0].projectName,
                isTotal: false,
                level: 2,
                hasSubChannels: true,
                ...firstChildData
              })
            }
            
                          // 添加其他子渠道（从第二个开始）
              directChildren.slice(1).forEach(child => {
                const childData = this.getChannelData(child.projectCode || child.projectId)
                this.tableData.push({
                  id: currentId++,
                  projectId: child.projectId,
                  projectCode: child.projectCode,
                  irrigationArea: '',
                  channelGroup: '',
                  channel: child.projectName,
                  isTotal: false,
                  level: 2,
                  hasSubChannels: true,
                  ...childData
                })
              })
            
            // 添加干渠直口合计（创建时先用空值，后面重新计算）
            this.tableData.push({
              id: currentId++,
              projectId: null,
              irrigationArea: '',
              channelGroup: '',
              channel: '干渠直口合计',
              isTotal: true,
              level: 2,
              hasSubChannels: false,
              flow8: '0.00',
              flow18: '0.00',
              dailyFlow: '0.00',
              cumulativeFlow: '0.00',
              remark: '',
              openTime: null,
              closeTime: null
            })
          }
        } catch (error) {
          console.error('构建干渠直口数据失败:', error)
        }
      },
      
      // 根据项目Code获取渠道数据
      getChannelData(projectCode) {
        if (!this.detailData || !this.detailData.waterSituationDetailVOList) {
          return {
            flow8: '',
            flow18: '',
            dailyFlow: '-',
            cumulativeFlow: '-',
            remark: '',
            openTime: null,
            closeTime: null
          }
        }
        
        const detail = this.detailData.waterSituationDetailVOList.find(
          item => item.channelNo === projectCode.toString()
        )
        
        if (detail) {
          return {
            flow8: detail.eightFlowRate || '',
            flow18: detail.eighteenFlowRate || '',
            dailyFlow: detail.dailyFlowRate != null ? detail.dailyFlowRate.toFixed(2) : '0.00',
            cumulativeFlow: detail.dailyFlowRateTotal != null ? detail.dailyFlowRateTotal.toFixed(2) : '0.00',
            remark: detail.remark || '',
            openTime: detail.openTime ? moment(detail.openTime, 'YYYY-MM-DD HH:mm:ss') : null,
            closeTime: detail.closeTime ? moment(detail.closeTime, 'YYYY-MM-DD HH:mm:ss') : null
          }
        }
        
        return {
          flow8: '',
          flow18: '',
          dailyFlow: '-',
          cumulativeFlow: '-',
          remark: '',
          openTime: null,
          closeTime: null
        }
      },
      
      // 计算灌域合计
      calculateDomainTotal(channels) {
        const details = this.detailData?.waterSituationDetailVOList || []
        let totalFlow8 = 0
        let totalFlow18 = 0
        let totalDailyFlow = 0
        let totalCumulativeFlow = 0
        
        channels.forEach(channel => {
          const detail = details.find(item => item.channelNo === (channel.projectCode || channel.projectId.toString()))
          if (detail) {
            totalFlow8 += parseFloat(detail.eightFlowRate) || 0
            totalFlow18 += parseFloat(detail.eighteenFlowRate) || 0
            totalDailyFlow += parseFloat(detail.dailyFlowRate) || 0
            totalCumulativeFlow += parseFloat(detail.dailyFlowRateTotal) || 0
          }
        })
        
        return {
          flow8: totalFlow8.toFixed(2),
          flow18: totalFlow18.toFixed(2),
          dailyFlow: totalDailyFlow.toFixed(2),
          cumulativeFlow: totalCumulativeFlow.toFixed(2),
          remark: '',
          openTime: null,
          closeTime: null
        }
      },

      // 计算合计行（参考新增页面的计算方式）
      calculateTotalFromTableData(totalRow) {
        // 根据合计行的类型找到需要统计的行
        let rowsToSum = []
        const totalIndex = this.tableData.findIndex(row => row.id === totalRow.id)
        
        if (totalRow.channelGroup === '灌域合计') {
          // 永济灌域合计：统计永济灌域下的所有非合计行
          rowsToSum = this.getRelatedRows(totalIndex, '永济灌域')
        } else if (totalRow.channelGroup === '永济渠合计') {
          // 永济渠合计：统计永济渠灌域下的所有非合计行
          rowsToSum = this.getRelatedRows(totalIndex, '永济渠灌域')
        } else if (totalRow.channel === '正稍渠合计') {
          // 正稍渠合计：统计正稍渠组下的所有非合计行
          rowsToSum = this.getSubChannelRows(totalIndex, '正稍渠')
        } else if (totalRow.channel === '干渠直口合计') {
          // 干渠直口合计：统计干渠直口组下的所有非合计行
          rowsToSum = this.getSubChannelRows(totalIndex, '干渠直口')
        }

        // 计算合计
        let totalFlow8 = 0
        let totalFlow18 = 0
        let totalDailyFlow = 0
        let totalCumulativeFlow = 0

        rowsToSum.forEach(row => {
          totalFlow8 += parseFloat(row.flow8) || 0
          totalFlow18 += parseFloat(row.flow18) || 0
          totalDailyFlow += parseFloat(row.dailyFlow) || 0
          totalCumulativeFlow += parseFloat(row.cumulativeFlow) || 0
        })

        return {
          flow8: totalFlow8 > 0 ? totalFlow8.toFixed(2) : '0.00',
          flow18: totalFlow18 > 0 ? totalFlow18.toFixed(2) : '0.00',
          dailyFlow: totalDailyFlow > 0 ? totalDailyFlow.toFixed(2) : '0.00',
          cumulativeFlow: totalCumulativeFlow > 0 ? totalCumulativeFlow.toFixed(2) : '0.00',
          remark: '',
          openTime: null,
          closeTime: null
        }
      },
      
      // 获取相关行（用于灌域合计）
      getRelatedRows(totalIndex, irrigationAreaName) {
        const rows = []
        
        // 向上查找，找到对应的灌域开始行
        let startIndex = -1
        for (let i = totalIndex - 1; i >= 0; i--) {
          const row = this.tableData[i]
          if (row.irrigationArea === irrigationAreaName) {
            startIndex = i
            break
          }
        }
        
        if (startIndex >= 0) {
          // 从灌域开始行到合计行之间的所有非合计行
          for (let i = startIndex; i < totalIndex; i++) {
            const row = this.tableData[i]
            if (!row.isTotal && row.projectId) {
              rows.push(row)
            }
          }
        }
        
        return rows
      },
      
      // 获取子渠道行（用于正稍渠和干渠直口合计）
      getSubChannelRows(totalIndex, channelGroupName) {
        const rows = []
        
        // 向上查找，找到对应的渠道组开始行
        let startIndex = -1
        for (let i = totalIndex - 1; i >= 0; i--) {
          const row = this.tableData[i]
          if (row.channelGroup === channelGroupName && row.hasSubChannels) {
            startIndex = i
            break
          }
        }
        
        if (startIndex >= 0) {
          // 从渠道组开始行到合计行之间的所有非合计行
          for (let i = startIndex; i < totalIndex; i++) {
            const row = this.tableData[i]
            if (!row.isTotal && row.projectId && row.level === 2) {
              rows.push(row)
            }
          }
        }
        
        return rows
      },
      
      // 重新计算所有合计行
      recalculateAllTotals() {
        const totalRows = this.tableData.filter(row => row.isTotal)
        totalRows.forEach(row => {
          const totalData = this.calculateTotalFromTableData(row)
          Object.assign(row, totalData)
        })
      },
      
      // 构建永济干渠水位流量表数据
      async buildCanalTableData() {
        try {
          // 获取永济干渠的闸数据
          const topLevelResponse = await getProjectChildren({ parentId: 0 })
          if (!topLevelResponse.success) return
          
          const topLevelProjects = topLevelResponse.data || []
          const yongjiProject = topLevelProjects.find(p => p.projectName?.includes('永济'))
          
          if (!yongjiProject) return
          
          const yongjiResponse = await getProjectChildren({ parentId: yongjiProject.projectId })
          if (!yongjiResponse.success) return
          
          const yongjiChildren = yongjiResponse.data || []
          // const yongjiCanal = yongjiChildren.find(p => p.projectName?.includes('永济渠'))
          const yongjiCanal = yongjiChildren.find(p => p.projectCode === 'GQ002')
          
          if (!yongjiCanal) return
          
          const yongjiCanalResponse = await getProjectChildren({ parentId: yongjiCanal.projectId })
          if (!yongjiCanalResponse.success) return
          
          const yongjiCanalChildren = yongjiCanalResponse.data || []
          
          // 获取闸项目
          const gates = yongjiCanalChildren.filter(p => p.projectName?.includes('闸'))
          
          const canalData = []
          let currentId = 1
          
          gates.forEach((gate, index) => {
            const gateData = this.getGateData(gate.projectCode || gate.projectId)
            
            // 闸上
            canalData.push({
              id: currentId++,
              canalName: index === 0 ? '永济干渠' : '', // 只有第一个闸显示永济干渠
              gateName: gate.projectName,
              gatePosition: '闸上',
              projectId: gate.projectId,
              projectCode: gate.projectCode,
              direction: 1,
              isTotal: false,
              ...gateData.above
            })
            
            // 闸下
            canalData.push({
              id: currentId++,
              canalName: '',
              gateName: '',
              gatePosition: '闸下',
              projectId: gate.projectId,
              projectCode: gate.projectCode,
              direction: 2,
              isTotal: false,
              ...gateData.below
            })
          })
          
          this.canalTableData = canalData
        } catch (error) {
          console.error('构建永济干渠表数据失败:', error)
        }
      },
      
      // 根据闸项目Code获取闸数据
      getGateData(gateCode) {
        if (!this.detailData || !this.detailData.waterStageDetailDtoList) {
          return {
            above: { waterLevel8: '', canalFlow8: '', waterLevel18: '', canalFlow18: '' },
            below: { waterLevel8: '', canalFlow8: '', waterLevel18: '', canalFlow18: '' }
          }
        }
        
        const aboveDetail = this.detailData.waterStageDetailDtoList.find(
          item => item.gateNo === gateCode.toString() && item.gateDirection === 1
        )
        
        const belowDetail = this.detailData.waterStageDetailDtoList.find(
          item => item.gateNo === gateCode.toString() && item.gateDirection === 2
        )
        
        return {
          above: {
            waterLevel8: aboveDetail?.eightWaterStage || '',
            canalFlow8: aboveDetail?.eightFlowRate || '',
            waterLevel18: aboveDetail?.eighteenWaterStage || '',
            canalFlow18: aboveDetail?.eighteenFlowRate || ''
          },
          below: {
            waterLevel8: belowDetail?.eightWaterStage || '',
            canalFlow8: belowDetail?.eightFlowRate || '',
            waterLevel18: belowDetail?.eighteenWaterStage || '',
            canalFlow18: belowDetail?.eighteenFlowRate || ''
          }
        }
      },
      
      // 永济干渠表合并方法
      mergeCanalRowMethod({ row, _rowIndex, column, visibleData }) {
        // 永济干渠列合并 - 整列合并
        if (column.property === 'canalName') {
          if (_rowIndex === 0) {
            return { rowspan: visibleData.length, colspan: 1 }
          } else {
            return { rowspan: 0, colspan: 0 }
          }
        }
        
        // 永济节制闸列合并 - 每个闸合并两行
        if (column.property === 'gateName') {
          const cellValue = row[column.property]
          if (cellValue) {
            // 检查是否是闸名称行（包含"闸"的项目名称）
            if (cellValue.includes('闸')) {
              return { rowspan: 2, colspan: 1 }
            }
          } else {
            // 空值的行需要隐藏（合并到上一行）
            return { rowspan: 0, colspan: 0 }
          }
        }
        
        // gatePosition列不需要合并，正常显示
        return null
      },

      // 单元格合并方法 - 与新增页面相同
      mergeRowMethod({ row, _rowIndex, column, visibleData }) {
        // 灌域列合并
        if (column.property === 'irrigationArea') {
          const cellValue = row[column.property]
          
          // 特殊处理：总干渠首行需要合并灌域和渠道两列（三格）
          if (cellValue === '总干渠首') {
            return { rowspan: 1, colspan: 3 }
          }
          
          // 永济灌域和永济渠灌域需要向下合并
          if (cellValue === '永济灌域' || cellValue === '永济渠灌域') {
            // 计算需要合并的行数
            let countRowspan = 1
            let nextRow = visibleData[_rowIndex + 1]
            
            if (cellValue === '永济灌域') {
              // 永济灌域向下合并到永济渠灌域之前（不包括永济渠灌域），再多合并一行
              while (nextRow && nextRow.irrigationArea === '' && nextRow.irrigationArea !== '永济渠灌域') {
                if (nextRow.channelGroup === '灌域合计') {
                  // 包含灌域合计行
                  countRowspan++
                  break
                }
                nextRow = visibleData[++countRowspan + _rowIndex]
              }
            } else if (cellValue === '永济渠灌域') {
              // 永济渠灌域向下合并到永济渠合计之前，再多合并一行
              while (nextRow && nextRow.irrigationArea === '') {
                if (nextRow.channelGroup === '永济渠合计') {
                  // 包含永济渠合计行
                  countRowspan++
                  break
                }
                nextRow = visibleData[++countRowspan + _rowIndex]
              }
            }
            
            if (countRowspan > 1) {
              return { rowspan: countRowspan, colspan: 1 }
            }
          }
          
          // 处理被合并行的灌域列隐藏
          if (!cellValue) {
            // 查找当前行是否属于某个灌域的合并范围
            for (let i = _rowIndex - 1; i >= 0; i--) {
              const prevRow = visibleData[i]
              if (prevRow.irrigationArea === '永济灌域' || prevRow.irrigationArea === '永济渠灌域') {
                // 检查当前行是否在合并范围内
                let shouldHide = false
                if (prevRow.irrigationArea === '永济灌域') {
                  shouldHide = row.irrigationArea !== '永济渠灌域'
                } else if (prevRow.irrigationArea === '永济渠灌域') {
                  shouldHide = true // 永济渠灌域下的所有行都被合并
                }
                
                if (shouldHide) {
                  return { rowspan: 0, colspan: 0 }
                }
                break
              }
              if (prevRow.irrigationArea) {
                break // 遇到其他灌域就停止查找
              }
            }
          }
          
          // 其他情况的灌域列合并（原有逻辑）
          if (cellValue) {
            const prevRow = visibleData[_rowIndex - 1]
            let nextRow = visibleData[_rowIndex + 1]
            if (prevRow && prevRow[column.property] === cellValue) {
              return { rowspan: 0, colspan: 0 }
            } else {
              let countRowspan = 1
              while (nextRow && nextRow[column.property] === cellValue) {
                nextRow = visibleData[++countRowspan + _rowIndex]
              }
              if (countRowspan > 1) {
                return { rowspan: countRowspan, colspan: 1 }
              }
            }
          }
        }
        
        // 渠道组列合并 - 针对有子渠道的情况
        if (column.property === 'channelGroup') {
          const cellValue = row[column.property]
          
          // 总干渠首行的渠道组列需要隐藏（已被灌域列合并）
          if (row.irrigationArea === '总干渠首') {
            return { rowspan: 0, colspan: 0 }
          }
          
          // 处理正稍渠和干渠直口的向下合并
          if (cellValue === '正稍渠' || cellValue === '干渠直口') {
            // 计算需要合并的行数，包括所有子渠道和合计行
            let countRowspan = 1
            let nextRow = visibleData[_rowIndex + 1]
            
            while (nextRow) {
              // 如果是同组的子渠道（channel不为空且hasSubChannels为true）
              if (nextRow.hasSubChannels && nextRow.channel && nextRow.channelGroup === '') {
                countRowspan++
                nextRow = visibleData[_rowIndex + countRowspan]
              }
              // 如果是该组的合计行
              else if (nextRow.channel === `${cellValue}合计` && nextRow.isTotal) {
                countRowspan++
                break
              }
              else {
                break
              }
            }
            
            if (countRowspan > 1) {
              return { rowspan: countRowspan, colspan: 1 }
            }
          }
          
          // 处理正稍渠和干渠直口子渠道的渠道组列隐藏
          if (!cellValue && row.hasSubChannels && row.channel) {
            // 检查是否属于正稍渠或干渠直口的子渠道
            for (let i = _rowIndex - 1; i >= 0; i--) {
              const prevRow = visibleData[i]
              if (prevRow.channelGroup === '正稍渠' || prevRow.channelGroup === '干渠直口') {
                return { rowspan: 0, colspan: 0 }
              }
              if (prevRow.channelGroup && prevRow.channelGroup !== '') {
                break
              }
            }
          }
          
          // 处理正稍渠合计和干渠直口合计行的渠道组列隐藏
          if (!cellValue && row.isTotal && (row.channel === '正稍渠合计' || row.channel === '干渠直口合计')) {
            return { rowspan: 0, colspan: 0 }
          }
          
          // 处理没有子渠道的行，渠道组列需要跨列显示（但排除总干渠首）
          if (cellValue && !row.hasSubChannels && row.channelGroup && row.irrigationArea !== '总干渠首') {
            return { rowspan: 1, colspan: 2 }
          }
        }
        
        // 渠道列处理
        if (column.property === 'channel') {
          // 总干渠首行的渠道列需要隐藏（已被灌域列合并）
          if (row.irrigationArea === '总干渠首') {
            return { rowspan: 0, colspan: 0 }
          }
          

          
          // 对于没有子渠道且渠道为空的行，隐藏渠道列
          if (!row.hasSubChannels && !row.channel) {
            return { rowspan: 0, colspan: 0 }
          }
        }
        
        return null
      },

      // 格式化日期
      formatDate(date) {
        if (!date) return '-'
        if (moment.isMoment(date)) {
          return date.format('YYYY-MM-DD')
        }
        return moment(date).format('YYYY-MM-DD')
      },

      // 格式化时间显示
      formatTime(time) {
        if (!time) return '-'
        if (moment.isMoment(time)) {
          return time.format('HH:mm')
        }
        return '-'
      },

      // 获取轮次标签
      getRoundLabel(value) {
        const option = this.roundOptions.find(item => item.value === value)
        return option ? option.label : '-'
      }
    }
  }

</script>

<style lang="less" scoped>
.irrigation-rounds-crop-reporting {
  background: #fff;

  .basic-info-section {
    margin-bottom: 30px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    
    .form-row {
      display: flex;
      gap: 30px;
      
      .form-item {
        display: flex;
        align-items: center;
        
        label {
          margin-right: 8px;
          font-weight: 500;
          color: #333;
        }
        
        .info-value {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }

  .water-table-section {
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    
    .water-table {
      width: 100%;
      
      /deep/ .vxe-table--border-line {
        border-color: #e8e8e8;
      }
      
      /deep/ .vxe-header--column {
        background-color: #fafafa;
        font-weight: 600;
      }
      
      /deep/ .vxe-body--row {
        &:nth-child(even) {
          background-color: #fafafa;
        }
      }
      
      /deep/ .vxe-table {
        width: 100% !important;
      }
      
      // 只读状态下的样式调整
      /deep/ .vxe-body--column {
        .vxe-cell {
          color: #333;
          font-size: 14px;
        }
      }
    }
  }

  .canal-table-section {
    margin-top: 30px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    
    .canal-table {
      width: 100%;
      
      /deep/ .vxe-table--border-line {
        border-color: #e8e8e8;
      }
      
      /deep/ .vxe-header--column {
        background-color: #fafafa;
        font-weight: 600;
      }
      
      /deep/ .vxe-body--row {
        &:nth-child(even) {
          background-color: #fafafa;
        }
      }
      
      /deep/ .vxe-table {
        width: 100% !important;
      }
      
      // 只读状态下的样式调整
      /deep/ .vxe-body--column {
        .vxe-cell {
          color: #333;
          font-size: 14px;
        }
      }
    }
  }
}
/deep/ .vxe-header--column,
/deep/ .vxe-body--column {
  text-align: center !important;
  justify-content: center !important;
  align-items: center !important;
}
</style>