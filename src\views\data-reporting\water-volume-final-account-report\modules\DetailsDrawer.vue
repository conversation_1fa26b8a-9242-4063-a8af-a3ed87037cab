<template>
  <!-- 增加修改 -->
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1500"
    @cancel="cancel"
    modalHeight="700"
  >
    <div slot="content">
      <a-row class="form-row" :gutter="32">
        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">基本信息</div>
        </a-col>
        <div class="form-border" style="width: 70%; margin-top: 30px">
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">年份：</label>
              <span class="common-value-text">
                {{ form?.accountYear }}
              </span>
            </div>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <div class="item">
              <label class="common-label-text">填报单位：</label>
              <span class="common-value-text">
                {{ reportUnitOptions.find(item => item.value == form?.depId)?.label }}
              </span>
            </div>
          </a-col>
        </div>

        <a-col :lg="24" :md="24" :sm="24" :span="24">
          <div class="title">水量决算</div>
        </a-col>
        <div class="form-border" style="width: 90%; height: 400px; margin-top: 60px">
          <div class="form-border-item">实用水量</div>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">春夏灌：</label>
              <span class="common-value-text">
                {{ getWaterAmount(1, 0, 1) ? `${getWaterAmount(1, 0, 1)}万m³` : `-` }}
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">秋灌：</label>
              <span class="common-value-text">{{ getWaterAmount(1, 0, 2) }}万m³</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">秋浇：</label>
              <span class="common-value-text">{{ getWaterAmount(1, 0, 3) }}万m³</span>
            </div>
          </a-col>
          <!-- item2 -->
          <div class="form-border-item">指标内直口实用水量</div>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">春夏灌：</label>
              <span class="common-value-text">{{ getWaterAmount(2, 1, 1) }}万m³</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">秋灌：</label>
              <span class="common-value-text">{{ getWaterAmount(2, 1, 2) }}万m³</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">秋浇：</label>
              <span class="common-value-text">{{ getWaterAmount(2, 1, 3) }}万m³</span>
            </div>
          </a-col>
          <!-- item3 指标内折算到斗口实用水量-->
          <div class="form-border-item">指标内折算到斗口实用水量</div>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">春夏灌：</label>
              <span class="common-value-text">{{ getWaterAmount(3, 1, 1) }}万m³</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">秋灌：</label>
              <span class="common-value-text">{{ getWaterAmount(3, 1, 2) }}万m³</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">秋浇：</label>
              <span class="common-value-text">{{ getWaterAmount(3, 1, 3) }}万m³</span>
            </div>
          </a-col>
          <!-- item4 -->
          <div class="form-border-item">指标外直口实用水量</div>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">春夏灌：</label>
              <span class="common-value-text">{{ getWaterAmount(2, 2, 1) }}万m³</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">秋灌：</label>
              <span class="common-value-text">{{ getWaterAmount(2, 2, 2) }}万m³</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">秋浇：</label>
              <span class="common-value-text">{{ getWaterAmount(2, 2, 3) }}万m³</span>
            </div>
          </a-col>
          <!-- item5 指标内折算到斗口实用水量-->
          <div class="form-border-item">指标外折算到斗口实用水量</div>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">春夏灌：</label>
              <span class="common-value-text">{{ getWaterAmount(3, 2, 1) }}万m³</span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">秋灌：</label>
              <span class="common-value-text">
                {{ getWaterAmount(3, 2, 2) ? `${getWaterAmount(3, 2, 2)}万m³` : `-` }}
              </span>
            </div>
          </a-col>
          <a-col :lg="8" :md="8" :sm="24">
            <div class="item">
              <label class="common-label-text">秋浇：</label>
              <span class="common-value-text">{{ getWaterAmount(3, 2, 3) }}万m³</span>
            </div>
          </a-col>
        </div>
      </a-row>

      <!-- <div class="details-img">
        <img src="@/assets/images/data-reporting/water-volume-final-account-report.png" alt="" />
      </div> -->
    </div>
  </ant-modal>
</template>

<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import { getIrrigationWaterAccount } from '../services'

  export default {
    name: 'FormDrawer',
    props: ['reportUnitOptions'],
    components: { AntModal },
    data() {
      return {
        open: false,
        modalLoading: false,
        formTitle: '',
        form: {},
      }
    },
    methods: {
      cancel() {
        this.open = false
      },
      showDetails(row) {
        this.open = true
        this.formTitle = '详情'
        getIrrigationWaterAccount({ accountYear: row.accountYear, depId: row.depId }).then(res => {
          this.form = { ...row, data: res.data }
          //   this.form = {
          //   ...row,
          //   practicalWaterVolume: {
          //     //实用水量
          //     SpringSummer: 13, //春夏灌
          //     AutumnIrrigation: 7.15, //秋灌
          //     AutumnIrrigation2: 11.7, //秋浇
          //   },
          //   //withinQuota：指标内 extraQuota：指标外 DirectOutlet：直口 Turnout：斗口
          //   withinQuotaDirectOutlet: {
          //     SpringSummer: 13.4, //春夏灌
          //     AutumnIrrigation: 6.89, //秋灌
          //     AutumnIrrigation2: 11.01, //秋浇
          //   }, //指标内直口实用水量
          //   withinQuotaTurnout: {
          //     SpringSummer: 31.5, //春夏灌
          //     AutumnIrrigation: 7.03, //秋灌
          //     AutumnIrrigation2: 8.66, //秋浇
          //   }, //指标内折算到斗口实用水量
          //   extraQuotaDirectOutlet: {
          //     SpringSummer: 13.5, //春夏灌
          //     AutumnIrrigation: 7.1, //秋灌
          //     AutumnIrrigation2: 10.7, //秋浇
          //   }, //指标外直口实用水量
          //   extraQuotaTurnout: {
          //     SpringSummer: 21.35, //春夏灌
          //     AutumnIrrigation: 5.21, //秋灌
          //     AutumnIrrigation2: 7.2, //秋浇
          //   }, //指标外折算到斗口实用水量
          // }
          console.log('详情 666', this.form, res)
        })
      },
      /** waterType水量类型 1 实用水量 2 直口 3 斗口  indicatorType指标类型 1 指标内 2指标外 0 全指标  irrigationType灌类型 1 春夏灌 2秋灌 3 秋浇 */
      getWaterAmount(waterType, indicatorType, irrigationType) {
        const item = this.form.data?.find(item => {
          return (
            item.waterType === waterType &&
            item.indicatorType === indicatorType &&
            item.irrigationType === irrigationType
          )
        })

        return item ? item.waterAmount : null
      },
    },
  }
</script>
<style lang="less" scoped>
  @import url('~@/global.less');
  .title {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }

  .details-img {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img {
      width: 100%;
      height: 100%;
    }
  }

  .form-border {
    border: 1px solid #e8e8e8;
    padding: 10px 20px;
    margin: 26px 14px;
    height: 50px;
  }
  .form-border-item {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
  }
</style>
