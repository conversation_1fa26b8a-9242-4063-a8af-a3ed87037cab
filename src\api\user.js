import request from '@/utils/request'

// 查询用户列表
export function listUser(data) {
  return request({
    url: '/sys/user/page',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: data,
  })
}

// 按部门分组人员树
export function userSelectTree(params) {
  return request({
    url: '/sys/dept/user/tree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 钉钉企业通讯录
export function dingUser() {
  return request({
    url: '/external/dingtalk/addressBook',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 组织机构树
export function getOrgTree() {
  return request({
    url: 'sys/dept/parent/tree',
    method: 'post',
  })
}
