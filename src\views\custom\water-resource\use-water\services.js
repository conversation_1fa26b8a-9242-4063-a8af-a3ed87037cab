import request from '@/utils/request'

// 列表分页查询
export function getPlanReportPage(data) {
  return request({
    url: '/model/plan/report/page',
    method: 'post',
    data,
  })
}

// 新增
export function addPlanReport(data) {
  return request({
    url: '/model/plan/report/add',
    method: 'post',
    data,
  })
}

// 删除
export function deletePlanReport(params) {
  return request({
    url: '/model/plan/report/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 详情
export function getPlanReportDetails(params) {
  return request({
    url: '/model/plan/report/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 用水计划修改
export function updatePlanReport(data) {
  return request({
    url: '/model/plan/report/updatePlanFlow',
    method: 'post',
    data,
  })
}

// 获取登录用户部门类型
export function getUserDeptType(params) {
  return request({
    url: '/sys/dept/user/type',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 获取用水计划信息
export function getPlanReportList(data) {
  return request({
    url: '/model/plan/report/getPlanReportList',
    method: 'post',
    data,
  })
}
